#!/usr/bin/env python3

import sys
sys.stdout = open(sys.stdout.fileno(), mode='w', buffering=1)
sys.stderr = open(sys.stderr.fileno(), mode='w', buffering=1)

import hydra
import time
from omegaconf import OmegaConf
import pathlib
from diffusion_policy_3d.workspace.base_workspace import BaseWorkspace
import tqdm
import torch
import os
import numpy as np
from termcolor import cprint
from torch.utils.data import DataLoader
from diffusion_policy_3d.common.pytorch_util import dict_apply
import copy
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import argparse

os.environ['WANDB_SILENT'] = "True"
OmegaConf.register_new_resolver("eval", eval, replace=True)

# Joint names for the 25-dimensional action space
# Based on joint32_to_joint25 mapping in gr1_action_util.py:
# waist 1 + head 2 + arm 5*2 + hand 6*2 = 25
# Each arm uses 5 out of 7 joints (skipping 2 joints per arm)
JOINT_NAMES = [
    # Waist (1 joint)
    "waist_pitch",              # 0: joint32[1] -> joint25[0]

    # Head (2 joints)
    "head_pitch",               # 1: joint32[3] -> joint25[1]
    "head_yaw",                 # 2: joint32[5] -> joint25[2]

    # Left arm (5 joints out of 7)
    "left_arm_joint_0",         # 3: joint32[6] -> joint25[3]
    "left_arm_joint_1",         # 4: joint32[7] -> joint25[4]
    "left_arm_joint_2",         # 5: joint32[8] -> joint25[5]
    "left_arm_joint_3",         # 6: joint32[9] -> joint25[6]
    "left_arm_joint_4",         # 7: joint32[10] -> joint25[7]

    # Right arm (5 joints out of 7)
    "right_arm_joint_0",        # 8: joint32[13] -> joint25[8]
    "right_arm_joint_1",        # 9: joint32[14] -> joint25[9]
    "right_arm_joint_2",        # 10: joint32[15] -> joint25[10]
    "right_arm_joint_3",        # 11: joint32[16] -> joint25[11]
    "right_arm_joint_4",        # 12: joint32[17] -> joint25[12]

    # Left hand (6 joints)
    "left_hand_joint_0",        # 13: joint32[20] -> joint25[13]
    "left_hand_joint_1",        # 14: joint32[21] -> joint25[14]
    "left_hand_joint_2",        # 15: joint32[22] -> joint25[15]
    "left_hand_joint_3",        # 16: joint32[23] -> joint25[16]
    "left_hand_joint_4",        # 17: joint32[24] -> joint25[17]
    "left_hand_joint_5",        # 18: joint32[25] -> joint25[18]

    # Right hand (6 joints)
    "right_hand_joint_0",       # 19: joint32[26] -> joint25[19]
    "right_hand_joint_1",       # 20: joint32[27] -> joint25[20]
    "right_hand_joint_2",       # 21: joint32[28] -> joint25[21]
    "right_hand_joint_3",       # 22: joint32[29] -> joint25[22]
    "right_hand_joint_4",       # 23: joint32[30] -> joint25[23]
    "right_hand_joint_5",       # 24: joint32[31] -> joint25[24]
]

def evaluate_joint_angles(workspace, cfg, checkpoint_path=None):
    """
    Evaluate model on dataset using real joint angle errors
    """
    cprint("=" * 60, "green")
    cprint("Joint Angle Evaluation", "green")
    cprint("=" * 60, "green")
    
    # Setup dataset first to get normalizer
    dataset = hydra.utils.instantiate(cfg.task.dataset)

    # Get normalizer from dataset
    normalizer = dataset.get_normalizer()

    # Load model with custom checkpoint if specified
    if checkpoint_path and checkpoint_path != workspace.get_checkpoint_path(tag='latest'):
        cprint(f"Loading custom checkpoint: {checkpoint_path}", "magenta")
        workspace.load_checkpoint(path=checkpoint_path)

    policy = workspace.get_model()
    policy.eval()

    device = torch.device(cfg.training.device)
    policy = policy.to(device)

    # Set normalizer to policy and move to device
    policy.set_normalizer(normalizer)
    # Ensure normalizer is on the correct device
    policy.normalizer = policy.normalizer.to(device)

    # Print validation set info
    from diffusion_policy_3d.common.sampler import get_val_mask
    val_mask = get_val_mask(
        n_episodes=dataset.replay_buffer.n_episodes,
        val_ratio=cfg.task.dataset.val_ratio,
        seed=cfg.task.dataset.seed
    )
    val_episodes = np.where(val_mask)[0]
    train_episodes = np.where(~val_mask)[0]

    cprint(f"Dataset configuration:", "yellow")
    cprint(f"  Total episodes: {dataset.replay_buffer.n_episodes}", "cyan")
    cprint(f"  Val ratio: {cfg.task.dataset.val_ratio}", "cyan")
    cprint(f"  Seed: {cfg.task.dataset.seed}", "cyan")
    cprint(f"  Validation episodes: {val_episodes}", "cyan")
    cprint(f"  Training episodes: {train_episodes}", "cyan")

    val_dataset = dataset.get_validation_dataset()
    if len(val_dataset) == 0:
        cprint("Warning: Empty validation dataset, using training dataset", "yellow")
        val_dataset = dataset

    val_dataloader = DataLoader(val_dataset, **cfg.val_dataloader)

    cprint(f"Dataset size: {len(val_dataset)}", "cyan")
    cprint(f"Batch size: {cfg.val_dataloader.batch_size}", "cyan")
    
    # Collect all predictions and ground truth (unnormalized)
    all_pred_actions = []
    all_gt_actions = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm.tqdm(val_dataloader, desc="Evaluating")):
            # Move data to device - manually ensure all tensors are on the correct device
            def move_to_device(data):
                if isinstance(data, dict):
                    return {k: move_to_device(v) for k, v in data.items()}
                elif isinstance(data, (list, tuple)):
                    return type(data)(move_to_device(item) for item in data)
                elif isinstance(data, torch.Tensor):
                    return data.to(device)
                else:
                    return data

            batch = move_to_device(batch)
            obs_dict = batch['obs']
            gt_action = batch['action']


            try:
                # Model prediction
                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']
                
                # Unnormalize both predictions and ground truth to get real joint angles
                pred_action_real = normalizer['action'].unnormalize(pred_action)
                gt_action_real = normalizer['action'].unnormalize(gt_action)
                
                # Convert to numpy and store
                all_pred_actions.append(pred_action_real.cpu().numpy())
                all_gt_actions.append(gt_action_real.cpu().numpy())
                
            except Exception as e:
                cprint(f"Batch {batch_idx} failed: {e}", "red")
                continue
            
            # Limit evaluation batches if specified
            if cfg.training.max_val_steps is not None and batch_idx >= cfg.training.max_val_steps - 1:
                break
    
    if not all_pred_actions:
        cprint("Evaluation failed: No successful batches", "red")
        return None
    
    # Concatenate all data
    all_pred_actions = np.concatenate(all_pred_actions, axis=0)  # [N, T, 25]
    all_gt_actions = np.concatenate(all_gt_actions, axis=0)      # [N, T, 25]
    
    N, T, action_dim = all_pred_actions.shape
    cprint(f"Total samples: {N}, Time steps: {T}, Action dimensions: {action_dim}", "cyan")
    
    # Calculate joint angle errors
    joint_errors = analyze_joint_angle_errors(all_pred_actions, all_gt_actions)

    # Create visualizations
    output_dir = pathlib.Path(workspace.output_dir)
    cprint("Creating visualizations...", "yellow")

    # Plot full episode predictions - this is what you requested!
    plot_full_episode_predictions(val_dataset, policy, device, JOINT_NAMES, output_dir,
                                episode_idx=0, joint_indices=[0, 3, 8, 13, 19])

    # Plot step-aligned predictions (main visualization you requested)
    plot_step_aligned_predictions(all_pred_actions, all_gt_actions, JOINT_NAMES, output_dir,
                                sample_idx=0, joint_indices=list(range(4)))

    # Plot prediction vs GT for first sample (first 6 joints)
    plot_prediction_vs_gt(all_pred_actions, all_gt_actions, JOINT_NAMES, output_dir,
                         sample_idx=0, joint_indices=list(range(6)))

    # Plot prediction vs GT for a few more samples if available
    if N > 1:
        plot_prediction_vs_gt(all_pred_actions, all_gt_actions, JOINT_NAMES, output_dir,
                             sample_idx=min(1, N-1), joint_indices=list(range(6)))

        # Also create step-aligned plot for second sample
        plot_step_aligned_predictions(all_pred_actions, all_gt_actions, JOINT_NAMES, output_dir,
                                    sample_idx=min(1, N-1), joint_indices=list(range(4)))

    # Plot multiple samples comparison for key joints
    key_joints = [0, 3, 8, 13, 19]  # waist, left_arm_0, right_arm_0, left_hand_0, right_hand_0
    for joint_idx in key_joints:
        if joint_idx < action_dim:
            plot_multiple_samples_comparison(all_pred_actions, all_gt_actions, JOINT_NAMES,
                                           output_dir, joint_idx=joint_idx, max_samples=3)

    # Save results
    results_file = output_dir / 'joint_angle_evaluation.npz'

    results = {
        'pred_actions': all_pred_actions,
        'gt_actions': all_gt_actions,
        'joint_names': JOINT_NAMES,
        **joint_errors
    }

    np.savez(results_file, **results)
    cprint(f"Results saved to: {results_file}", "green")

    return results

def analyze_joint_angle_errors(pred_actions, gt_actions):
    """
    Analyze joint angle errors in detail
    """
    N, T, action_dim = pred_actions.shape
    
    # Calculate absolute errors for each joint at each timestep
    abs_errors = np.abs(pred_actions - gt_actions)  # [N, T, 25]
    
    # Overall statistics
    overall_mae = np.mean(abs_errors)
    overall_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2))
    
    # Per-joint statistics (averaged over all samples and timesteps)
    joint_mae = np.mean(abs_errors, axis=(0, 1))  # [25,]
    joint_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2, axis=(0, 1)))  # [25,]
    joint_max_error = np.max(abs_errors, axis=(0, 1))  # [25,]
    
    # Per-timestep statistics (averaged over all samples and joints)
    timestep_mae = np.mean(abs_errors, axis=(0, 2))  # [T,]
    timestep_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2, axis=(0, 2)))  # [T,]
    
    # Print detailed analysis
    cprint("=" * 60, "cyan")
    cprint("Joint Angle Error Analysis", "cyan")
    cprint("=" * 60, "cyan")
    
    cprint(f"Overall MAE: {overall_mae:.4f} radians ({np.degrees(overall_mae):.2f} degrees)", "green")
    cprint(f"Overall RMSE: {overall_rmse:.4f} radians ({np.degrees(overall_rmse):.2f} degrees)", "green")
    
    cprint(f"\nPer-Joint Analysis:", "yellow")
    cprint("-" * 80, "white")
    cprint(f"{'Joint':<25} {'MAE (rad)':<12} {'MAE (deg)':<12} {'RMSE (rad)':<12} {'RMSE (deg)':<12} {'Max Error (deg)':<15}", "white")
    cprint("-" * 80, "white")
    
    for i in range(action_dim):
        joint_name = JOINT_NAMES[i] if i < len(JOINT_NAMES) else f"joint_{i}"
        cprint(f"{joint_name:<25} {joint_mae[i]:<12.4f} {np.degrees(joint_mae[i]):<12.2f} "
               f"{joint_rmse[i]:<12.4f} {np.degrees(joint_rmse[i]):<12.2f} {np.degrees(joint_max_error[i]):<15.2f}", "white")
    
    # Find best and worst joints
    best_joint_idx = np.argmin(joint_mae)
    worst_joint_idx = np.argmax(joint_mae)
    
    cprint(f"\nBest Joint: {JOINT_NAMES[best_joint_idx]} (MAE: {np.degrees(joint_mae[best_joint_idx]):.2f} deg)", "green")
    cprint(f"Worst Joint: {JOINT_NAMES[worst_joint_idx]} (MAE: {np.degrees(joint_mae[worst_joint_idx]):.2f} deg)", "red")
    
    cprint(f"\nPer-Timestep Analysis:", "yellow")
    for t in range(T):
        cprint(f"  Timestep {t:2d}: MAE = {timestep_mae[t]:.4f} rad ({np.degrees(timestep_mae[t]):.2f} deg)", "white")
    
    best_timestep = np.argmin(timestep_mae)
    worst_timestep = np.argmax(timestep_mae)
    cprint(f"\nBest Timestep: {best_timestep} (MAE: {np.degrees(timestep_mae[best_timestep]):.2f} deg)", "green")
    cprint(f"Worst Timestep: {worst_timestep} (MAE: {np.degrees(timestep_mae[worst_timestep]):.2f} deg)", "red")
    
    return {
        'overall_mae': overall_mae,
        'overall_rmse': overall_rmse,
        'joint_mae': joint_mae,
        'joint_rmse': joint_rmse,
        'joint_max_error': joint_max_error,
        'timestep_mae': timestep_mae,
        'timestep_rmse': timestep_rmse,
        'abs_errors': abs_errors
    }

def plot_prediction_vs_gt(pred_actions, gt_actions, joint_names, output_dir, sample_idx=0, joint_indices=None):
    """
    Plot prediction vs ground truth for specific joints with time alignment

    Args:
        pred_actions: [N, T, 25] prediction array
        gt_actions: [N, T, 25] ground truth array
        joint_names: list of joint names
        output_dir: directory to save plots
        sample_idx: which sample to plot (default: 0)
        joint_indices: list of joint indices to plot (default: first 6 joints)
    """
    if joint_indices is None:
        joint_indices = list(range(min(6, len(joint_names))))  # Plot first 6 joints by default

    N, T, action_dim = pred_actions.shape

    if sample_idx >= N:
        cprint(f"Warning: sample_idx {sample_idx} >= total samples {N}, using sample 0", "yellow")
        sample_idx = 0

    # Get data for the specific sample
    pred_sample = pred_actions[sample_idx]  # [T, 25]
    gt_sample = gt_actions[sample_idx]      # [T, 25]

    # Create color map for different prediction steps
    colors = plt.cm.tab10(np.linspace(0, 1, T))

    # Create subplots
    n_joints = len(joint_indices)
    fig, axes = plt.subplots(n_joints, 1, figsize=(12, 3*n_joints))
    if n_joints == 1:
        axes = [axes]

    for i, joint_idx in enumerate(joint_indices):
        ax = axes[i]
        joint_name = joint_names[joint_idx] if joint_idx < len(joint_names) else f"joint_{joint_idx}"

        # Plot GT as black line
        gt_values = gt_sample[:, joint_idx]
        time_steps = np.arange(T)
        ax.plot(time_steps, np.degrees(gt_values), 'k-', linewidth=2, label='Ground Truth', alpha=0.8)

        # Plot predictions with different colors for each step
        for t in range(T):
            pred_value = pred_sample[t, joint_idx]
            ax.plot(t, np.degrees(pred_value), 'o', color=colors[t], markersize=6,
                   label=f'Pred Step {t}' if i == 0 else "")  # Only show legend for first subplot

        # Connect prediction points with lines of the same color
        pred_values = pred_sample[:, joint_idx]
        ax.plot(time_steps, np.degrees(pred_values), '--', color='red', alpha=0.6, linewidth=1,
               label='Pred Trajectory' if i == 0 else "")

        ax.set_xlabel('Time Step')
        ax.set_ylabel('Joint Angle (degrees)')
        ax.set_title(f'{joint_name} - Sample {sample_idx}')
        ax.grid(True, alpha=0.3)

        # Add error annotation
        mae = np.mean(np.abs(pred_values - gt_values))
        ax.text(0.02, 0.98, f'MAE: {np.degrees(mae):.2f}°',
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # Add legend only to the first subplot
    if n_joints > 0:
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()

    # Save plot
    output_path = pathlib.Path(output_dir) / f'prediction_vs_gt_sample_{sample_idx}.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    cprint(f"Plot saved to: {output_path}", "green")
    plt.close()

def plot_multiple_samples_comparison(pred_actions, gt_actions, joint_names, output_dir,
                                   joint_idx=0, sample_indices=None, max_samples=5):
    """
    Plot multiple samples for a single joint to show prediction consistency

    Args:
        pred_actions: [N, T, 25] prediction array
        gt_actions: [N, T, 25] ground truth array
        joint_names: list of joint names
        output_dir: directory to save plots
        joint_idx: which joint to plot
        sample_indices: list of sample indices to plot (default: first few samples)
        max_samples: maximum number of samples to plot
    """
    N, T, action_dim = pred_actions.shape

    if sample_indices is None:
        sample_indices = list(range(min(max_samples, N)))

    joint_name = joint_names[joint_idx] if joint_idx < len(joint_names) else f"joint_{joint_idx}"

    fig, ax = plt.subplots(1, 1, figsize=(12, 6))

    # Colors for different samples
    sample_colors = plt.cm.Set1(np.linspace(0, 1, len(sample_indices)))

    for i, sample_idx in enumerate(sample_indices):
        if sample_idx >= N:
            continue

        pred_sample = pred_actions[sample_idx, :, joint_idx]
        gt_sample = gt_actions[sample_idx, :, joint_idx]
        time_steps = np.arange(T)

        color = sample_colors[i]

        # Plot GT
        ax.plot(time_steps, np.degrees(gt_sample), '-', color=color, linewidth=2,
               alpha=0.8, label=f'GT Sample {sample_idx}')

        # Plot predictions
        ax.plot(time_steps, np.degrees(pred_sample), '--', color=color, linewidth=1.5,
               alpha=0.7, label=f'Pred Sample {sample_idx}')

        # Mark prediction points
        ax.scatter(time_steps, np.degrees(pred_sample), color=color, s=30, alpha=0.8, zorder=5)

    ax.set_xlabel('Time Step')
    ax.set_ylabel('Joint Angle (degrees)')
    ax.set_title(f'{joint_name} - Multiple Samples Comparison')
    ax.grid(True, alpha=0.3)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()

    # Save plot
    output_path = pathlib.Path(output_dir) / f'multiple_samples_{joint_name}.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    cprint(f"Multi-sample plot saved to: {output_path}", "green")
    plt.close()

def plot_full_episode_predictions(dataset, policy, device, joint_names, output_dir,
                                episode_idx=0, joint_indices=None):
    """
    Plot full episode predictions showing GT trajectory and all prediction segments

    Args:
        dataset: validation dataset
        policy: trained policy model
        device: device to run inference on
        joint_names: list of joint names
        output_dir: directory to save plots
        episode_idx: which episode to visualize
        joint_indices: list of joint indices to plot
    """
    if joint_indices is None:
        joint_indices = [0, 3, 8, 13, 19]  # Default key joints

    cprint(f"Generating full episode predictions for episode {episode_idx}...", "cyan")

    # Get episode data directly from replay buffer
    replay_buffer = dataset.replay_buffer

    # Check if episode exists
    if episode_idx >= replay_buffer.n_episodes:
        cprint(f"Warning: Episode {episode_idx} does not exist. Total episodes: {replay_buffer.n_episodes}", "yellow")
        episode_idx = 0

    # Get the full episode data
    episode_data = replay_buffer.get_episode(episode_idx)
    episode_length = len(episode_data['action'])

    cprint(f"Episode {episode_idx} length: {episode_length} steps", "cyan")

    cprint(f"Episode length: {episode_length} steps", "cyan")

    # Extract GT trajectory for the full episode
    gt_trajectory = episode_data['action']  # [episode_length, 25]
    if isinstance(gt_trajectory, torch.Tensor):
        gt_trajectory = gt_trajectory.cpu().numpy()
    elif not isinstance(gt_trajectory, np.ndarray):
        gt_trajectory = np.array(gt_trajectory)

    # Generate predictions for each step
    pred_trajectories = []  # Will store all prediction segments

    # Get observation data for the episode
    obs_data = {
        'agent_pos': episode_data['state'],  # [episode_length, 32]
        'point_cloud': episode_data['point_cloud']  # [episode_length, num_points, 6]
    }

    # Convert to numpy if needed
    for key in obs_data:
        if isinstance(obs_data[key], torch.Tensor):
            obs_data[key] = obs_data[key].cpu().numpy()
        elif not isinstance(obs_data[key], np.ndarray):
            obs_data[key] = np.array(obs_data[key])

    # Apply point cloud processing
    import diffusion_policy_3d.model.vision_3d.point_process as point_process

    # Create new array with correct shape for processed point clouds
    processed_point_clouds = np.zeros((episode_length, dataset.num_points, obs_data['point_cloud'].shape[-1]))

    for i in range(episode_length):
        # point_process.uniform_sampling_numpy expects shape [B, N, C], but we have [N, C]
        # So we need to add batch dimension
        pc = obs_data['point_cloud'][i]  # [N, C]
        pc_batch = pc[np.newaxis, ...]  # [1, N, C]
        pc_sampled = point_process.uniform_sampling_numpy(pc_batch, dataset.num_points)  # [1, num_points, C]
        processed_point_clouds[i] = pc_sampled[0]  # [num_points, C]

    # Replace the original point cloud data
    obs_data['point_cloud'] = processed_point_clouds

    with torch.no_grad():
        for step_idx in range(episode_length):
            # Create observation for this step (using horizon window)
            horizon = dataset.horizon
            start_obs_idx = max(0, step_idx - horizon + 1)
            end_obs_idx = step_idx + 1

            # Extract observation window
            obs_window = {
                'agent_pos': obs_data['agent_pos'][start_obs_idx:end_obs_idx],
                'point_cloud': obs_data['point_cloud'][start_obs_idx:end_obs_idx]
            }

            # Pad if necessary
            if len(obs_window['agent_pos']) < horizon:
                pad_length = horizon - len(obs_window['agent_pos'])
                for key in obs_window:
                    pad_shape = (pad_length,) + obs_window[key].shape[1:]
                    pad_data = np.tile(obs_window[key][:1], (pad_length,) + (1,) * (len(obs_window[key].shape) - 1))
                    obs_window[key] = np.concatenate([pad_data, obs_window[key]], axis=0)

            # Convert to torch and move to device
            obs_dict = {}
            for key, value in obs_window.items():
                obs_dict[key] = torch.from_numpy(value.astype(np.float32)).unsqueeze(0).to(device)

            try:
                # Get prediction
                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']  # [1, T, 25]

                # Convert to numpy and store
                pred_traj = pred_action[0].cpu().numpy()  # [T, 25]
                pred_trajectories.append(pred_traj)

            except Exception as e:
                cprint(f"Prediction failed at step {step_idx}: {e}", "red")
                # Use zeros as fallback
                pred_traj = np.zeros((16, 25))  # Assume 16 prediction steps
                pred_trajectories.append(pred_traj)

    # Create plots for each joint
    for joint_idx in joint_indices:
        if joint_idx >= len(joint_names):
            continue

        joint_name = joint_names[joint_idx]

        fig, ax = plt.subplots(1, 1, figsize=(20, 8))

        # Plot GT trajectory
        time_steps = np.arange(episode_length)
        gt_joint_values = gt_trajectory[:, joint_idx]
        ax.plot(time_steps, np.degrees(gt_joint_values), 'k-', linewidth=3,
               label='Ground Truth', alpha=0.9, zorder=10)

        # Plot all prediction segments
        colors = plt.cm.viridis(np.linspace(0, 1, len(pred_trajectories)))

        for step_idx, pred_traj in enumerate(pred_trajectories):
            if step_idx >= episode_length:
                break

            pred_joint_values = pred_traj[:, joint_idx]  # [T]
            pred_time_steps = np.arange(step_idx, min(step_idx + len(pred_joint_values), episode_length))

            # Truncate prediction if it goes beyond episode length
            if len(pred_time_steps) < len(pred_joint_values):
                pred_joint_values = pred_joint_values[:len(pred_time_steps)]

            # Plot prediction segment
            ax.plot(pred_time_steps, np.degrees(pred_joint_values),
                   color=colors[step_idx], alpha=0.6, linewidth=1,
                   label=f'Pred from step {step_idx}' if step_idx < 5 else "")

        # Formatting
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Joint Angle (degrees)')
        ax.set_title(f'{joint_name} - Full Episode Predictions (Episode {episode_idx})')
        ax.grid(True, alpha=0.3)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # Add statistics
        if len(pred_trajectories) > 0:
            # Calculate error for first prediction step only
            first_pred_errors = []
            for step_idx, pred_traj in enumerate(pred_trajectories):
                if step_idx < len(gt_joint_values):
                    error = abs(pred_traj[0, joint_idx] - gt_joint_values[step_idx])
                    first_pred_errors.append(error)

            if first_pred_errors:
                mean_error = np.mean(first_pred_errors)
                ax.text(0.02, 0.98, f'Mean 1-step error: {np.degrees(mean_error):.2f}°',
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()

        # Save plot
        output_path = output_dir / f'full_episode_predictions_{joint_name}_ep{episode_idx}.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        cprint(f"Full episode plot saved to: {output_path}", "green")
        plt.close()

def plot_step_aligned_predictions(pred_actions, gt_actions, joint_names, output_dir,
                                sample_idx=0, joint_indices=None):
    """
    Plot predictions with step-by-step alignment showing how each prediction step
    aligns with the ground truth timeline

    Args:
        pred_actions: [N, T, 25] prediction array
        gt_actions: [N, T, 25] ground truth array
        joint_names: list of joint names
        output_dir: directory to save plots
        sample_idx: which sample to plot
        joint_indices: list of joint indices to plot
    """
    if joint_indices is None:
        joint_indices = list(range(min(4, len(joint_names))))  # Plot first 4 joints by default

    N, T, _ = pred_actions.shape

    if sample_idx >= N:
        cprint(f"Warning: sample_idx {sample_idx} >= total samples {N}, using sample 0", "yellow")
        sample_idx = 0

    # Get data for the specific sample
    pred_sample = pred_actions[sample_idx]  # [T, 25]
    gt_sample = gt_actions[sample_idx]      # [T, 25]

    # Create figure with subplots
    n_joints = len(joint_indices)
    fig, axes = plt.subplots(n_joints, 1, figsize=(15, 4*n_joints))
    if n_joints == 1:
        axes = [axes]

    # Define colors for different prediction steps
    step_colors = plt.cm.viridis(np.linspace(0, 1, T))

    for i, joint_idx in enumerate(joint_indices):
        ax = axes[i]
        joint_name = joint_names[joint_idx] if joint_idx < len(joint_names) else f"joint_{joint_idx}"

        # Plot GT as thick black line
        gt_values = gt_sample[:, joint_idx]
        time_steps = np.arange(T)
        ax.plot(time_steps, np.degrees(gt_values), 'k-', linewidth=3,
               label='Ground Truth', alpha=0.9, zorder=10)

        # Plot each prediction step as a colored line segment
        for step in range(T):
            # For each step, we show the prediction value at that step
            pred_value = pred_sample[step, joint_idx]

            # Draw a vertical line from GT to prediction at this step
            gt_value = gt_sample[step, joint_idx]
            ax.plot([step, step], [np.degrees(gt_value), np.degrees(pred_value)],
                   color=step_colors[step], linewidth=2, alpha=0.7)

            # Mark the prediction point
            ax.plot(step, np.degrees(pred_value), 'o', color=step_colors[step],
                   markersize=8, markeredgecolor='white', markeredgewidth=1,
                   label=f'Step {step}' if i == 0 and step < 5 else "")  # Limit legend entries

        # Connect all prediction points with a dashed line
        pred_values = pred_sample[:, joint_idx]
        ax.plot(time_steps, np.degrees(pred_values), '--', color='red',
               linewidth=2, alpha=0.8, label='Prediction Trajectory' if i == 0 else "")

        # Add error bars or shaded region
        errors = np.abs(pred_values - gt_values)
        ax.fill_between(time_steps,
                       np.degrees(gt_values - errors),
                       np.degrees(gt_values + errors),
                       alpha=0.2, color='red', label='Error Range' if i == 0 else "")

        # Formatting
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Joint Angle (degrees)')
        ax.set_title(f'{joint_name} - Step-by-Step Prediction Alignment (Sample {sample_idx})')
        ax.grid(True, alpha=0.3)

        # Add statistics
        mae = np.mean(np.abs(pred_values - gt_values))
        rmse = np.sqrt(np.mean((pred_values - gt_values) ** 2))
        ax.text(0.02, 0.98, f'MAE: {np.degrees(mae):.2f}°\nRMSE: {np.degrees(rmse):.2f}°',
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # Set reasonable y-limits
        all_values = np.concatenate([np.degrees(gt_values), np.degrees(pred_values)])
        y_margin = (np.max(all_values) - np.min(all_values)) * 0.1
        ax.set_ylim(np.min(all_values) - y_margin, np.max(all_values) + y_margin)

    # Add legend to the first subplot
    if n_joints > 0:
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

    plt.tight_layout()

    # Save plot
    output_path = pathlib.Path(output_dir) / f'step_aligned_predictions_sample_{sample_idx}.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    cprint(f"Step-aligned plot saved to: {output_path}", "green")
    plt.close()

@hydra.main(
    version_base=None,
    config_path=str(pathlib.Path(__file__).parent.joinpath(
        'diffusion_policy_3d','config'))
)
def main(cfg: OmegaConf):
    torch.manual_seed(42)
    OmegaConf.resolve(cfg)

    cprint(f"Config: {cfg._target_}", "yellow")
    cprint(f"Task: {cfg.task_name}", "yellow")
    cprint(f"Data path: {cfg.task.dataset.zarr_path}", "yellow")

    cls = hydra.utils.get_class(cfg._target_)
    workspace: BaseWorkspace = cls(cfg)

    # Check if model exists - first try environment variable or command line
    checkpoint_path = None

    # Try environment variable first
    env_checkpoint = os.environ.get('CHECKPOINT_PATH')
    if env_checkpoint:
        checkpoint_path = pathlib.Path(env_checkpoint)
        if not checkpoint_path.exists():
            cprint(f"Warning: Environment checkpoint not found: {env_checkpoint}", "yellow")
            checkpoint_path = None

    # If no checkpoint specified or not found, use default
    if checkpoint_path is None:
        checkpoint_path = workspace.get_checkpoint_path(tag='latest')
        if not checkpoint_path.exists():
            cprint(f"Error: Model file not found {checkpoint_path}", "red")
            return

    cprint(f"Using model: {checkpoint_path}", "green")
    
    # Start evaluation
    results = evaluate_joint_angles(workspace, cfg, checkpoint_path)
    
    if results:
        cprint("Joint angle evaluation completed!", "green")
    else:
        cprint("Joint angle evaluation failed!", "red")

if __name__ == "__main__":
    main()
